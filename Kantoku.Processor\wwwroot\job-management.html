<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kantoku Job Management</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .job-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 10px 0; }
        .job-header { display: flex; justify-content: between; align-items: center; }
        .job-title { font-size: 18px; font-weight: bold; }
        .job-status { padding: 4px 8px; border-radius: 4px; color: white; }
        .status-enabled { background-color: #28a745; }
        .status-disabled { background-color: #dc3545; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .templates { margin: 20px 0; }
        .template-item { display: inline-block; margin: 5px; padding: 5px 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; cursor: pointer; }
        .template-item:hover { background: #e9ecef; }
        .alert { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .alert-success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .alert-error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kantoku Job Management</h1>
        <p>Manage Hangfire recurring jobs dynamically without restarting the application.</p>
        
        <div id="alerts"></div>
        
        <div class="templates">
            <h3>Cron Expression Templates</h3>
            <div id="cronTemplates"></div>
        </div>
        
        <div id="jobsList"></div>
        
        <button class="btn btn-primary" onclick="refreshJobs()">Refresh Jobs</button>
    </div>

    <script>
        const API_BASE = '/api/JobManagement';
        let selectedCronInput = null;

        async function loadJobs() {
            try {
                const response = await fetch(`${API_BASE}/recurring-jobs`);
                const jobs = await response.json();
                displayJobs(jobs);
            } catch (error) {
                showAlert('Error loading jobs: ' + error.message, 'error');
            }
        }

        async function loadCronTemplates() {
            try {
                const response = await fetch(`${API_BASE}/cron-templates`);
                const templates = await response.json();
                displayCronTemplates(templates);
            } catch (error) {
                console.error('Error loading cron templates:', error);
            }
        }

        function displayJobs(jobs) {
            const container = document.getElementById('jobsList');
            container.innerHTML = '';
            
            jobs.forEach(job => {
                const jobCard = document.createElement('div');
                jobCard.className = 'job-card';
                jobCard.innerHTML = `
                    <div class="job-header">
                        <div class="job-title">${job.id}</div>
                        <div class="job-status ${job.error ? 'status-disabled' : 'status-enabled'}">
                            ${job.error ? 'Error' : 'Active'}
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Cron Expression:</label>
                        <input type="text" id="cron-${job.id}" value="${job.cron}" 
                               onfocus="selectedCronInput = this">
                    </div>
                    <div class="form-group">
                        <label>Next Execution:</label>
                        <input type="text" value="${job.nextExecution ? new Date(job.nextExecution).toLocaleString() : 'N/A'}" readonly>
                    </div>
                    <div class="form-group">
                        <label>Last Execution:</label>
                        <input type="text" value="${job.lastExecution ? new Date(job.lastExecution).toLocaleString() : 'Never'}" readonly>
                    </div>
                    ${job.error ? `<div class="alert alert-error">Error: ${job.error}</div>` : ''}
                    <div>
                        <button class="btn btn-primary" onclick="updateJobCron('${job.id}')">Update Schedule</button>
                        <button class="btn btn-success" onclick="triggerJob('${job.id}')">Trigger Now</button>
                        <button class="btn btn-danger" onclick="disableJob('${job.id}')">Disable</button>
                    </div>
                `;
                container.appendChild(jobCard);
            });
        }

        function displayCronTemplates(templates) {
            const container = document.getElementById('cronTemplates');
            container.innerHTML = '';
            
            templates.forEach(template => {
                const templateItem = document.createElement('div');
                templateItem.className = 'template-item';
                templateItem.title = template.description;
                templateItem.textContent = `${template.name} (${template.expression})`;
                templateItem.onclick = () => {
                    if (selectedCronInput) {
                        selectedCronInput.value = template.expression;
                    }
                };
                container.appendChild(templateItem);
            });
        }

        async function updateJobCron(jobId) {
            const cronInput = document.getElementById(`cron-${jobId}`);
            const cronExpression = cronInput.value.trim();
            
            if (!cronExpression) {
                showAlert('Please enter a cron expression', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/recurring-jobs/${jobId}/cron`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cronExpression })
                });
                
                if (response.ok) {
                    showAlert(`Job ${jobId} schedule updated successfully`, 'success');
                    loadJobs();
                } else {
                    const error = await response.text();
                    showAlert(`Error updating job: ${error}`, 'error');
                }
            } catch (error) {
                showAlert('Error updating job: ' + error.message, 'error');
            }
        }

        async function triggerJob(jobId) {
            try {
                const response = await fetch(`${API_BASE}/recurring-jobs/${jobId}/trigger`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    showAlert(`Job ${jobId} triggered successfully`, 'success');
                } else {
                    const error = await response.text();
                    showAlert(`Error triggering job: ${error}`, 'error');
                }
            } catch (error) {
                showAlert('Error triggering job: ' + error.message, 'error');
            }
        }

        async function disableJob(jobId) {
            if (!confirm(`Are you sure you want to disable job ${jobId}?`)) return;
            
            try {
                const response = await fetch(`${API_BASE}/recurring-jobs/${jobId}/status`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ enabled: false })
                });
                
                if (response.ok) {
                    showAlert(`Job ${jobId} disabled successfully`, 'success');
                    loadJobs();
                } else {
                    const error = await response.text();
                    showAlert(`Error disabling job: ${error}`, 'error');
                }
            } catch (error) {
                showAlert('Error disabling job: ' + error.message, 'error');
            }
        }

        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type === 'error' ? 'error' : 'success'}`;
            alert.textContent = message;
            alertsContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function refreshJobs() {
            loadJobs();
        }

        // Load initial data
        loadJobs();
        loadCronTemplates();
        
        // Auto-refresh every 30 seconds
        setInterval(loadJobs, 30000);
    </script>
</body>
</html>
