using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using System.Text;

namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Base class for Hangfire job implementations with logging and error handling
/// </summary>
public abstract class HangfireJobBase : IHangfireJob
{
    protected readonly ILogger Logger;
    protected readonly IServiceProvider ServiceProvider;
    private readonly StringBuilder _executionLog = new();
    private PerformContext? _performContext;

    protected HangfireJobBase(ILogger logger, IServiceProvider serviceProvider)
    {
        Logger = logger;
        ServiceProvider = serviceProvider;
    }

    /// <summary>
    /// Main execution method that needs to be implemented by derived classes
    /// </summary>
    public abstract Task ExecuteAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Hangfire entry point - calls Execute with injected PerformContext
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task ExecuteHangfireJob(PerformContext context, CancellationToken cancellationToken = default)
    {
        await Execute(context, cancellationToken);
    }

    /// <summary>
    /// Executes the job with comprehensive error handling and logging
    /// </summary>
    private async Task Execute(PerformContext context, CancellationToken cancellationToken = default)
    {
        // Store context for use in logging methods
        _performContext = context;

        var jobName = GetType().Name;
        var startTime = DateTime.UtcNow;

        // Log to both standard logger and Hangfire console
        Logger.LogInformation("Starting execution of job {JobName} at {StartTime}", jobName, startTime);
        context.WriteLine($"🚀 Starting {jobName} at {startTime:yyyy-MM-dd HH:mm:ss} UTC");

        LogToExecutionLog($"Job {jobName} started at {startTime:yyyy-MM-dd HH:mm:ss} UTC");

        try
        {
            // Set up progress reporting
            var progress = context.WriteProgressBar();
            progress.SetValue(0);

            await ExecuteAsync(cancellationToken);

            progress.SetValue(100);

            var duration = DateTime.UtcNow - startTime;
            var successMessage = $"✅ Successfully completed {jobName} in {duration.TotalMilliseconds:F0}ms";

            Logger.LogInformation("Successfully completed job {JobName} in {Duration}ms",
                jobName, duration.TotalMilliseconds);
            context.WriteLine(successMessage);
            LogToExecutionLog(successMessage);

            // Store execution log in job storage for dashboard viewing
            context.SetJobParameter("ExecutionLog", GetExecutionLog());
        }
        catch (OperationCanceledException)
        {
            var cancelMessage = $"⚠️ Job {jobName} was cancelled";
            Logger.LogWarning("Job {JobName} was cancelled", jobName);
            context.WriteLine(cancelMessage);
            LogToExecutionLog(cancelMessage);
            context.SetJobParameter("ExecutionLog", GetExecutionLog());
            throw;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            var errorMessage = $"❌ Job {jobName} failed after {duration.TotalMilliseconds:F0}ms: {ex.Message}";

            Logger.LogError(ex, "Job {JobName} failed after {Duration}ms: {ErrorMessage}",
                jobName, duration.TotalMilliseconds, ex.Message);
            context.WriteLine(errorMessage);
            LogToExecutionLog($"{errorMessage}\nException: {ex}");
            context.SetJobParameter("ExecutionLog", GetExecutionLog());
            throw;
        }
        finally
        {
            // Clear context
            _performContext = null;
        }
    }

    /// <summary>
    /// Creates a service scope for dependency injection
    /// </summary>
    protected IServiceScope CreateScope()
    {
        return ServiceProvider.CreateScope();
    }

    /// <summary>
    /// Logs information message
    /// </summary>
    protected void LogInformation(string message, params object[] args)
    {
        var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
        var timestampedMessage = $"[{DateTime.UtcNow:HH:mm:ss}] ℹ️ {formattedMessage}";

        Logger.LogInformation("{JobName}: {Message}", GetType().Name, formattedMessage);
        LogToExecutionLog(timestampedMessage);

        // Try to write to Hangfire console if context is available
        try
        {
            _performContext?.WriteLine(timestampedMessage);
        }
        catch
        {
            // Ignore if no context available
        }
    }

    /// <summary>
    /// Logs warning message
    /// </summary>
    protected void LogWarning(string message, params object[] args)
    {
        var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
        var timestampedMessage = $"[{DateTime.UtcNow:HH:mm:ss}] ⚠️ {formattedMessage}";

        Logger.LogWarning("{JobName}: {Message}", GetType().Name, formattedMessage);
        LogToExecutionLog(timestampedMessage);

        try
        {
            _performContext?.WriteLine(timestampedMessage);
        }
        catch
        {
            // Ignore if no context available
        }
    }

    /// <summary>
    /// Logs error message
    /// </summary>
    protected void LogError(string message, Exception? ex = null, params object[] args)
    {
        var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
        var timestampedMessage = $"[{DateTime.UtcNow:HH:mm:ss}] ❌ {formattedMessage}";

        if (ex != null)
        {
            Logger.LogError(ex, "{JobName}: {Message}", GetType().Name, formattedMessage);
            LogToExecutionLog($"{timestampedMessage}\nException: {ex.Message}\nStack Trace: {ex.StackTrace}");
        }
        else
        {
            Logger.LogError("{JobName}: {Message}", GetType().Name, formattedMessage);
            LogToExecutionLog(timestampedMessage);
        }

        try
        {
            _performContext?.WriteLine(timestampedMessage);
        }
        catch
        {
            // Ignore if no context available
        }
    }

    /// <summary>
    /// Adds a message to the execution log
    /// </summary>
    private void LogToExecutionLog(string message)
    {
        _executionLog.AppendLine(message);
    }

    /// <summary>
    /// Gets the complete execution log
    /// </summary>
    protected string GetExecutionLog()
    {
        return _executionLog.ToString();
    }

    /// <summary>
    /// Checks if cancellation has been requested and throws if it has
    /// </summary>
    protected static void ThrowIfCancellationRequested(CancellationToken cancellationToken)
    {
        cancellationToken.ThrowIfCancellationRequested();
    }
}
