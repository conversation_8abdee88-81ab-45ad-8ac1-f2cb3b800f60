using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Jobs.Hangfire;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Processor.Jobs;

/// <summary>
/// Hangfire job that automatically checks out employees who haven't checked out
/// after their scheduled shift end time (after 1 hour)
/// </summary>
public class AutoCheckoutHangfireJob : HangfireJobBase
{
    public AutoCheckoutHangfireJob(ILogger<AutoCheckoutHangfireJob> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public override async Task ExecuteAsync(CancellationToken cancellationToken = default)
    {
        LogInformation("Starting auto checkout job execution");

        using var scope = CreateScope();
        var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        try
        {
            // Calculate the cutoff time - 1 hour ago in UTC
            var oneHourAgo = DateTime.Now.AddHours(-1);

            var shiftsToUpdate = await appDbContext.EmployeeShifts
                .Where(es =>
                    es.ScheduledStartTime.HasValue &&
                    es.ScheduledEndTime.HasValue &&
                    es.ScheduledEndTime < oneHourAgo &&
                    !es.CheckOutTime.HasValue &&
                    !es.AutoCheckOutTime.HasValue)
                .ToListAsync(cancellationToken);

            LogInformation("Found {0} employee shifts that need auto checkout", shiftsToUpdate.Count);

            // Update each shift to set the AutoCheckOutTime equal to their ScheduledEndTime
            foreach (var shift in shiftsToUpdate)
            {
                ThrowIfCancellationRequested(cancellationToken);

                shift.AutoCheckOutTime = shift.ScheduledEndTime;
                LogInformation("Auto checking out employee shift {0} with scheduled end time {1}",
                    shift.EmployeeShiftUid, shift.ScheduledEndTime);
            }

            // Save changes to the database
            int updatedCount = await appDbContext.SaveChangesAsync(cancellationToken);

            LogInformation("Successfully auto checked out {0} employee shifts", updatedCount);
        }
        catch (OperationCanceledException)
        {
            LogWarning("Auto checkout job was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            LogError("Error executing auto checkout job: {0}", ex, ex.Message);
            throw;
        }
    }
}
