using Kantoku.Processor.Configurations;
using Kantoku.Processor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Kantoku.Processor.Data.Contexts;

/// <summary>
/// Database context for main application entities
/// </summary>
public class AppDbContext : DbContext
{
    public AppDbConfig AppDbConfig { get; }
    public AppDbContext(DbContextOptions<AppDbContext> options, IOptions<AppDbConfig> appDbConfig)
        : base(options)
    {
        AppDbConfig = appDbConfig.Value;
    }

    public DbSet<EmployeeShift> EmployeeShifts { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<DeviceToken> DeviceTokens { get; set; }
    public DbSet<Ranking> Rankings { get; set; }
    public DbSet<EmployeeCost> EmployeeCosts { get; set; }
    public DbSet<EventCalendar> EventCalendars { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseNpgsql(AppDbConfig.BuildConnectionString());

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<EmployeeShift>(entity =>
        {
            entity.HasKey(e => e.EmployeeShiftUid).HasName("employee_shift_pkey");

            entity.ToTable("employee_shift", AppDbConfig.Schema);

            entity.Property(e => e.EmployeeShiftUid)
                .HasColumnName("employee_shift_uid");
            entity.Property(e => e.EmployeeUid)
                .HasColumnName("employee_uid");
            entity.Property(e => e.ScheduledStartTime)
                .HasColumnType("timestamp")
                .HasColumnName("schedule_start_time");
            entity.Property(e => e.ScheduledEndTime)
                 .HasColumnType("timestamp")
                 .HasColumnName("schedule_end_time");
            entity.Property(e => e.CheckInTime)
                .HasColumnType("timestamp")
                .HasColumnName("check_in_time");
            entity.Property(e => e.CheckOutTime)
                 .HasColumnType("timestamp")
                 .HasColumnName("check_out_time");
            entity.Property(e => e.CheckInLocation)
                .HasColumnName("check_in_location");
            entity.Property(e => e.CheckOutLocation)
                .HasColumnName("check_out_location");
            entity.Property(e => e.AutoCheckOutTime)
                .HasColumnType("timestamp")
                .HasColumnName("auto_check_out_time");
        });

        modelBuilder.Entity<DeviceToken>(entity =>
        {
            entity.HasKey(e => e.DeviceUid).HasName("device_pkey");

            entity.ToTable("device_token", AppDbConfig.Schema);

            entity.Property(e => e.DeviceUid)
                .HasColumnName("device_token_uid");
            entity.Property(e => e.EmployeeUid)
                .HasColumnName("employee_uid");
            entity.Property(e => e.FirebaseToken)
                .HasColumnName("firebase_token");
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            entity.HasKey(e => e.EmployeeUid).HasName("employee_pkey");

            entity.ToTable("employee", AppDbConfig.Schema);

            entity.Property(e => e.EmployeeUid)
                .HasColumnName("employee_uid");
            entity.Property(e => e.AccountUid)
                .HasColumnName("account_uid");
            entity.Property(e => e.OrgUid)
                .HasColumnName("org_uid");
            entity.Property(e => e.IsDeleted)
                .HasColumnName("is_deleted");
            entity.Property(e => e.SalaryInMonth)
                .HasColumnName("salary_in_month");
            entity.Property(e => e.RankingUid)
                .HasColumnName("ranking_uid");

            entity.HasOne(e => e.Ranking)
                .WithMany(r => r.Employees)
                .HasForeignKey(e => e.RankingUid)
                .HasConstraintName("employee_ranking_uid_fkey");
        });

        modelBuilder.Entity<Ranking>(entity =>
        {
            entity.HasKey(e => e.RankingUid).HasName("ranking_pkey");

            entity.ToTable("ranking", AppDbConfig.Schema);

            entity.Property(e => e.RankingUid)
                .HasColumnName("ranking_uid");
            entity.Property(e => e.MaxValue)
                .HasColumnName("max_value");
            entity.Property(e => e.MinValue)
                .HasColumnName("min_value");
            entity.Property(e => e.Description)
                .HasColumnName("description");
            entity.Property(e => e.IsDeleted)
                .HasColumnName("is_deleted");
            entity.Property(e => e.OrgUid)
                .HasColumnName("org_uid");
        });

        modelBuilder.Entity<EmployeeCost>(entity =>
        {
            entity.HasKey(e => e.EmployeeCostUid).HasName("employee_cost_pkey");

            entity.ToTable("employee_cost", AppDbConfig.Schema);

            entity.Property(e => e.EmployeeCostUid)
                .HasColumnName("employee_cost_uid");
            entity.Property(e => e.EmployeeUid)
                .HasColumnName("employee_uid");
            entity.Property(e => e.EffectiveDate)
                .HasColumnName("effective_date")
                .HasColumnType("date");
            entity.Property(e => e.DailyCostAmount)
                .HasColumnName("daily_cost_amount");
        });

        modelBuilder.Entity<EventCalendar>(entity =>
        {
            entity.HasKey(e => e.EventUid).HasName("event_calendar_pkey");

            entity.ToTable("event_calendar", AppDbConfig.Schema);

            entity.Property(e => e.EventUid)
                .HasColumnName("event_id");
            entity.Property(e => e.EventStartDate)
                .HasColumnType("date")
                .HasColumnName("event_start_date");
            entity.Property(e => e.EventEndDate)
                .HasColumnType("date")
                .HasColumnName("event_end_date");
            entity.Property(e => e.IsRecurring)
                .HasColumnName("is_recurring");
            entity.Property(e => e.RecurringFrom)
                .HasColumnType("date")
                .HasColumnName("recurring_from");
            entity.Property(e => e.RecurringTo)
                .HasColumnType("date")
                .HasColumnName("recurring_to");
            entity.Property(e => e.RecurringDay)
                .HasColumnType("smallint[]")
                .HasColumnName("recurring_day");
            entity.Property(e => e.RecurringWeek)
                .HasColumnType("smallint[]")
                .HasColumnName("recurring_week");
            entity.Property(e => e.RecurringMonth)
                .HasColumnType("smallint[]")
                .HasColumnName("recurring_month");
            entity.Property(e => e.OrgUid)
                .HasColumnName("org_uid");
            entity.Property(e => e.IsDayOff)
                .HasDefaultValue(true)
                .HasColumnName("is_day_off");
            entity.Property(e => e.IsDeleted)
                .HasDefaultValue(false)
                .HasColumnName("is_deleted");
        });

        modelBuilder.Entity<Project>(entity =>
        {
            entity.HasKey(e => e.ProjectUid).HasName("project_pkey");

            entity.ToTable("project", AppDbConfig.Schema);

            entity.Property(e => e.ProjectUid)
                .HasColumnName("project_uid");
            entity.Property(e => e.OrgUid)
                .HasColumnName("org_uid");
            entity.Property(e => e.Address)
                .HasColumnName("address");
            entity.Property(e => e.StatusCode)
                .HasColumnName("status_code");
            entity.Property(e => e.IsDeleted)
                .HasColumnName("is_deleted");
        });
    }
}
