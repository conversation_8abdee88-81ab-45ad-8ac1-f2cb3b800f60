using EventBus.Events;
using EventBus.Interfaces;
using EventBus.Kafka;
using Kantoku.Processor.Jobs;
using Kantoku.Processor.Services;
using Kantoku.Processor.Services.EventHandlers;
using Kantoku.Processor.Services.Interfaces;

namespace Kantoku.Processor.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// configure hosted services
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddHostedServices(this IServiceCollection services)
    {
        // Add independent background service for Kafka event processing
        services.AddHostedService<KafkaEventConsumerBackgroundService>();

        return services;
    }

    /// <summary>
    /// configure firebase service
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddServices(this IServiceCollection services)
    {
        // Configure Firebase service for notifications
        services.AddScoped<IFirebaseService, FirebaseService>();

        return services;
    }

    /// <summary>
    /// configure hangfire jobs
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddJobs(this IServiceCollection services)
    {
        // Register Hangfire jobs
        services.AddScoped<AutoCheckoutHangfireJob>();
        services.AddScoped<CheckOutReminderHangfireJob>();
        services.AddScoped<EmployeeCostCalculateHangfireJob>();

        return services;
    }

    public static IServiceCollection AddEventHandlers(this IServiceCollection services)
    {
        // Register event consumer
        services.AddSingleton<IEventConsumer, KafkaEventConsumer>();

        // Register event handlers
        services.AddScoped<IEventHandler<AttendanceEvent>, AttendanceEventHandler>();

        return services;
    }
}
