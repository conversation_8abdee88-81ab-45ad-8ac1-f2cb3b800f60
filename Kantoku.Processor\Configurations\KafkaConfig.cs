namespace Kantoku.Processor.Configurations;

/// <summary>
/// Kafka configuration settings
/// </summary>
public class KafkaConfig
{
    /// <summary>
    /// Kafka bootstrap servers
    /// </summary>
    public required string BootstrapServers { get; set; }

    /// <summary>
    /// Consumer group ID
    /// </summary>
    public required string GroupId { get; set; }

    /// <summary>
    /// Auto offset reset policy
    /// </summary>
    public required string AutoOffsetReset { get; set; }

    /// <summary>
    /// Enable auto commit
    /// </summary>
    public bool EnableAutoCommit { get; set; } 

    /// <summary>
    /// Session timeout in milliseconds
    /// </summary>
    public int SessionTimeoutMs { get; set; } 

    /// <summary>
    /// Default topic prefix
    /// </summary>
    public required string TopicPrefix { get; set; }

    /// <summary>
    /// Number of partitions for new topics
    /// </summary>
    public int DefaultPartitions { get; set; } 

    /// <summary>
    /// Replication factor for new topics
    /// </summary>
    public short DefaultReplicationFactor { get; set; }
}

/// <summary>
/// Kafka producer specific configuration
/// </summary>
public class KafkaProducerConfig : KafkaConfig
{
    /// <summary>
    /// Acknowledgment level
    /// </summary>
    public required string Acks { get; set; }

    /// <summary>
    /// Batch size
    /// </summary>
    public int BatchSize { get; set; } = 16384;

    /// <summary>
    /// Linger time in milliseconds
    /// </summary>
    public int LingerMs { get; set; } = 5;
}

/// <summary>
/// Kafka consumer specific configuration
/// </summary>
public class KafkaConsumerConfig : KafkaConfig
{
    /// <summary>
    /// Fetch minimum bytes
    /// </summary>
    public int FetchMinBytes { get; set; } = 1;

    /// <summary>
    /// Maximum poll interval in milliseconds
    /// </summary>
    public int MaxPollIntervalMs { get; set; } = 600000; // 10 minutes

    /// <summary>
    /// Heartbeat interval in milliseconds
    /// </summary>
    public int HeartbeatIntervalMs { get; set; } = 3000;

    /// <summary>
    /// Auto commit interval in milliseconds
    /// </summary>
    public int AutoCommitIntervalMs { get; set; } = 5000;
}
