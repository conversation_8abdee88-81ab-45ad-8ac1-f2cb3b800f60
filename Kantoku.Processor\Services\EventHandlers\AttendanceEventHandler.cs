using System.Text.Json;
using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Distributed;

namespace Kantoku.Processor.Services.EventHandlers;

/// <summary>
/// <PERSON>les check-in events to find the nearest project
/// </summary>
public class AttendanceEventHandler : IEventHandler<AttendanceEvent>
{
    private readonly AppDbContext _appDbContext;
    private readonly AppConfig _appConfig;
    private readonly ILogger<AttendanceEventHandler> _logger;
    private readonly IDistributedCache _cache;

    public AttendanceEventHandler(
        AppDbContext appDbContext,
        IOptions<AppConfig> appConfig,
        ILogger<AttendanceEventHandler> logger,
        IDistributedCache cache)
    {
        _appDbContext = appDbContext;
        _appConfig = appConfig.Value;
        _logger = logger;
        _cache = cache;
    }

    public async Task HandleAsync(AttendanceEvent @event, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing check-in event for shiftId {ShiftId} at coordinates {Lat}, {Lng}",
                @event.ShiftId, @event.Coordinates.Latitude, @event.Coordinates.Longitude);

            // Validate GPS coordinates
            if (!@event.Coordinates.IsValid)
            {
                _logger.LogWarning("Invalid GPS coordinates provided: {Lat}, {Lng}",
                    @event.Coordinates.Latitude, @event.Coordinates.Longitude);
                return;
            }

            var currentEmployeeShift = await _appDbContext.Set<EmployeeShift>()
                .Where(e => e.EmployeeShiftUid == @event.ShiftId)
                .FirstOrDefaultAsync(cancellationToken);

            if (currentEmployeeShift is null)
            {
                _logger.LogWarning("No employee shift found for shiftId {ShiftId}", @event.ShiftId);
                return;
            }

            // Get all active projects for the organization
            var projects = await GetProjectListAsync(@event.OrgId, cancellationToken);
            if (!projects.Any())
            {
                _logger.LogWarning("No active projects found for organization {OrgId}", @event.OrgId);
                return;
            }

            // Find the nearest project
            var nearestProject = FindNearestProject(@event.Coordinates, projects);
            if (nearestProject != null)
            {
                _logger.LogInformation("Found nearest project {ProjectId}", nearestProject.ProjectUid);

                // Process the nearest project match
                FindNearestProject(@event.Coordinates, projects);
            }
            else
            {
                _logger.LogWarning("No nearby projects found for check-in location");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing check-in event for shiftId {ShiftId}", @event.ShiftId);
        }
    }

    private async Task<IEnumerable<Project>> GetProjectListAsync(Guid orgId, CancellationToken cancellationToken)
    {
        string cacheKey = $"projectList:{orgId}";
        var cachedProjects = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (!string.IsNullOrEmpty(cachedProjects))
        {
            _logger.LogDebug("Project list for OrgId {OrgId} found in cache.", orgId);
            return JsonSerializer.Deserialize<List<Project>>(cachedProjects) ?? Enumerable.Empty<Project>();
        }

        _logger.LogDebug("Project list for OrgId {OrgId} not found in cache. Fetching from DB.", orgId);
        try
        {
            var projectsFromDb = await _appDbContext.Set<Project>()
                .Where(p => p.OrgUid == orgId)
                .Where(p => !p.IsDeleted)
                .Where(p => p.StatusCode == "STARTED") // Assuming this is the active status
                .Where(p => !string.IsNullOrEmpty(p.Address))
                .ToListAsync(cancellationToken);

            if (!projectsFromDb.Any())
            {
                _logger.LogInformation("No active projects with addresses found for organization {OrgId} in DB.", orgId);
                // Cache an empty list to avoid repeated DB calls for non-existent/empty data
                await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(Enumerable.Empty<Project>()), 
                    new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(10)), cancellationToken);
                return Enumerable.Empty<Project>();
            }

            var projectsWithCoordinates = await GetProjectCoordinatesAsync(projectsFromDb, cancellationToken);
            
            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1)); // Cache for 1 hour
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(projectsWithCoordinates), cacheEntryOptions, cancellationToken);
            _logger.LogDebug("Project list for OrgId {OrgId} cached.", orgId);

            return projectsWithCoordinates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active projects for organization {OrgId}", orgId);
            return Enumerable.Empty<Project>();
        }
    }

    private Project? FindNearestProject(
        GpsCoordinates checkInCoordinates,
        IEnumerable<Project> projects,
        double threshold = 50.0)
    {
        if (!projects.Any())
            return null;

        var projectDistances = CalculateProjectDistanace(checkInCoordinates, projects, threshold);
        if (!projectDistances.Any())
            return null;

        var closest = projectDistances.OrderBy(d => d.Distance).FirstOrDefault();
        if (closest is null)
            return null;

        return projects.FirstOrDefault(p => p.ProjectUid == closest.ProjectUid);
    }

    private IEnumerable<ProjectDistance> CalculateProjectDistanace(
        GpsCoordinates checkInCoordinates,
        IEnumerable<Project> projects,
        double threshold = 50.0)
    {
        if (checkInCoordinates is null || checkInCoordinates.Latitude is null || checkInCoordinates.Longitude is null)
            yield break;
        if (projects is null || !projects.Any())
            yield break;

        foreach (var project in projects)
        {
            if (project.Latitude is null || project.Longitude is null)
                continue;
            var distance = CalculateDistance(
                checkInCoordinates.Latitude.Value, checkInCoordinates.Longitude.Value,
                project.Latitude.Value, project.Longitude.Value);
            if (distance <= threshold)
                yield return new ProjectDistance(project.ProjectUid, distance);
        }
    }

    private async Task<IEnumerable<Project>> GetProjectCoordinatesAsync(
        IEnumerable<Project> projects,
        CancellationToken cancellationToken)
    {
        var getCoordinateTasks = projects.Select(async project =>
        {
            var coordinates = await GetProjectCoordinatesAsync(project.Address, cancellationToken);
            return new Project
            {
                ProjectUid = project.ProjectUid,
                Address = project.Address,
                Latitude = coordinates?.Latitude,
                Longitude = coordinates?.Longitude
            };
        });
        return await Task.WhenAll(getCoordinateTasks);
    }

    private async Task<Coordinates?> GetProjectCoordinatesAsync(string? address, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(address))
            return null;

        string cacheKey = $"coordinates:{Uri.EscapeDataString(address)}";
        var cachedCoordinates = await _cache.GetStringAsync(cacheKey, cancellationToken);

        if (!string.IsNullOrEmpty(cachedCoordinates))
        {
            _logger.LogDebug("Coordinates for address '{Address}' found in cache.", address);
            return JsonSerializer.Deserialize<Coordinates>(cachedCoordinates);
        }

        _logger.LogDebug("Coordinates for address '{Address}' not found in cache. Fetching from API.", address);
        string requestUri = string.Format("https://maps.googleapis.com/maps/api/geocode/json?key={1}&address={0}", Uri.EscapeDataString(address), _appConfig.GeoCodingAPK);
        var client = new HttpClient();
        try
        {
            var responseMessage = await client.GetAsync(requestUri, cancellationToken);
            if (!responseMessage.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to fetch coordinates for address '{Address}'. Status: {StatusCode}", address, responseMessage.StatusCode);
                return null;
            }
            var responseStream = await responseMessage.Content.ReadAsStreamAsync(cancellationToken);
            var geocodingResponse = await JsonSerializer.DeserializeAsync<GeocodingResponseDto>(responseStream, cancellationToken: cancellationToken);
            if (geocodingResponse is null || geocodingResponse.Results is null || geocodingResponse.Results.Count == 0)
            {
                _logger.LogWarning("No geocoding results for address '{Address}'.", address);
                return null;
            }
            var coordinates = new Coordinates(geocodingResponse.Results.First().Geometry.Location.Lat, geocodingResponse.Results.First().Geometry.Location.Lng);

            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromHours(1)); // Cache for 1 hour, extendable
            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(coordinates), cacheEntryOptions, cancellationToken);
            _logger.LogDebug("Coordinates for address '{Address}' cached.", address);

            return coordinates;
        }
        catch (JsonException jsonEx)
        {
            _logger.LogError(jsonEx, "Error deserializing geocoding response for address '{Address}'.", address);
            return null;
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error getting project coordinates for address '{Address}' from API.", address);
            return null;
        }
    }

    private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        // Haversine formula to calculate distance between two GPS coordinates
        const double earthRadiusKm = 6371.0;

        var dLat = DegreesToRadians(lat2 - lat1);
        var dLon = DegreesToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return earthRadiusKm * c;
    }

    private double DegreesToRadians(double degrees)
    {
        return degrees * Math.PI / 180.0;
    }

    private record ProjectDistance(Guid ProjectUid, double Distance);

    private record Coordinates(double Latitude, double Longitude);
}